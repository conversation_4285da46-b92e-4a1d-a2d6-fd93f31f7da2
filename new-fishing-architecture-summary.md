# 摸鱼功能新架构总结

## 问题背景

原有架构在处理跨日期摸鱼时存在以下问题：
1. 时间可视化图表不能正确显示当前摸鱼时间段
2. 结束摸鱼始终提示摸鱼时长为0分钟，未记录
3. 时间计算逻辑分散，导致数据不一致

## 新架构设计

### 核心思想
统一时间计算逻辑，确保所有组件使用一致的摸鱼时间计算方法。

### 1. 扩展摸鱼状态数据结构

```javascript
const fishingState = {
  workId: workId,
  date: formatDateKey(date),                    // 记录归属日期
  startTime: now.toISOString(),                 // ISO格式开始时间
  startMinutes: actualStartMinutes,             // 在时间段中的实际开始位置
  originalStartMinutes: currentMinutes,         // 原始开始时间（今天的时间）
  workSegment: workSegment,                     // 工作时间段
  remark: remark,                               // 备注
  isActive: true,                               // 是否活跃
  isCrossDateSegment: isFromYesterday,          // 是否跨日期时间段
  displayDate: isFromYesterday ? formatDateKey(new Date()) : formatDateKey(date) // 显示日期
}
```

### 2. 统一时间计算服务

在`fishing-manager.js`中添加统一的时间计算方法：

#### calculateActualCurrentMinutes()
```javascript
// 计算当前时间在摸鱼时间段中的实际位置
calculateActualCurrentMinutes(fishingState, currentMinutes) {
  if (fishingState.isCrossDateSegment) {
    return currentMinutes + 1440  // 跨日期：转换到跨日期时间段位置
  } else {
    return currentMinutes         // 普通：直接使用当前时间
  }
}
```

#### calculateCurrentFishingDuration()
```javascript
// 计算当前摸鱼时长（分钟）
calculateCurrentFishingDuration() {
  const fishingState = this.getFishingState()
  if (!fishingState || !fishingState.isActive) return 0
  
  const now = new Date()
  const currentMinutes = now.getHours() * 60 + now.getMinutes()
  const currentActualMinutes = this.calculateActualCurrentMinutes(fishingState, currentMinutes)
  
  return Math.max(0, currentActualMinutes - fishingState.startMinutes)
}
```

#### getCurrentFishingDurationString()
```javascript
// 获取格式化的摸鱼时长字符串
getCurrentFishingDurationString() {
  const durationMinutes = this.calculateCurrentFishingDuration()
  return this.formatFishingDuration(durationMinutes)
}
```

### 3. 修改结束摸鱼逻辑

```javascript
endFishing(saveFishingRecord) {
  // 使用统一的时长计算
  const durationMinutes = this.calculateCurrentFishingDuration()
  const currentActualMinutes = this.calculateActualCurrentMinutes(fishingState, currentMinutes)
  
  // 创建摸鱼记录时使用正确的结束时间
  const fishingRecord = {
    id: 0,
    start: fishingState.startMinutes,
    end: currentActualMinutes,  // 使用计算后的结束时间
    remark: fishingState.remark
  }
}
```

### 4. 统一组件时间显示

#### Dashboard1组件
```javascript
updateCurrentSegmentDuration: function() {
  if (this.data.isFishing) {
    // 摸鱼状态：使用统一的摸鱼时长计算
    const dataManager = getApp().getDataManager()
    formattedDuration = dataManager.getCurrentFishingDurationString()
  } else {
    // 普通工作：使用原有的时长计算
    // ... 原有逻辑
  }
}
```

#### Fishing-Control组件
```javascript
updateFishingDuration() {
  // 使用统一的摸鱼时长计算
  const dataManager = getApp().getDataManager()
  const formattedDuration = dataManager.getCurrentFishingDurationString()
  
  this.setData({ fishingDuration: formattedDuration })
}
```

## 解决的问题

### 1. 时间可视化图表显示问题
**原因**：图表显示的是昨天的数据，但摸鱼记录保存到了今天
**解决**：
- 跨日期摸鱼记录正确保存到昨天
- 摸鱼状态包含正确的显示日期信息
- 图表能正确显示跨日期摸鱼时间段

### 2. 结束摸鱼时长为0的问题
**原因**：时长计算使用了错误的时间差
**解决**：
- 使用统一的`calculateCurrentFishingDuration()`方法
- 正确处理跨日期时间段中的时间位置
- 确保时长计算的一致性

### 3. 时间调整功能问题
**原因**：边界检查使用了不一致的时间计算
**解决**：
- 所有时间计算都使用`calculateActualCurrentMinutes()`
- 边界检查基于正确的时长计算
- 时间调整功能在跨日期场景下正常工作

## 数据流示例

### 跨日期摸鱼场景
```
设置：昨天22:00-今天02:00加班 (1320-1560分钟)
当前：今天01:00 (60分钟)

开始摸鱼：
- 检测：在昨天的跨日期时间段中
- 保存：
  * date: 昨天
  * startMinutes: 1500 (60 + 1440)
  * originalStartMinutes: 60
  * isCrossDateSegment: true
  * displayDate: 今天

时长计算：
- currentMinutes: 60
- currentActualMinutes: 1500 (60 + 1440)
- duration: 1500 - 1500 = 0分钟

结束摸鱼：
- 当前时间：今天01:30 (90分钟)
- currentActualMinutes: 1530 (90 + 1440)
- duration: 1530 - 1500 = 30分钟
- 摸鱼记录：start=1500, end=1530, 保存到昨天
```

### 普通摸鱼场景
```
设置：今天09:00-18:00工作 (540-1080分钟)
当前：今天14:00 (840分钟)

开始摸鱼：
- 检测：在今天的工作时间段中
- 保存：
  * date: 今天
  * startMinutes: 840
  * originalStartMinutes: 840
  * isCrossDateSegment: false
  * displayDate: 今天

时长计算：
- currentMinutes: 840
- currentActualMinutes: 840 (无变化)
- duration: 840 - 840 = 0分钟

结束摸鱼：
- 当前时间：今天14:30 (870分钟)
- currentActualMinutes: 870
- duration: 870 - 840 = 30分钟
- 摸鱼记录：start=840, end=870, 保存到今天
```

## 修改的文件

1. `miniprogram/core/managers/fishing-manager.js`
   - 扩展摸鱼状态数据结构
   - 添加统一时间计算方法
   - 修改结束摸鱼逻辑

2. `miniprogram/core/managers/data-manager.js`
   - 添加对外时间计算接口

3. `miniprogram/components/dashboard1/index.js`
   - 修改摸鱼时间显示逻辑

4. `miniprogram/components/fishing-control/index.js`
   - 修改摸鱼时间显示逻辑

## 测试验证

### 测试场景1：跨日期摸鱼完整流程
1. 设置昨天22:00-今天02:00加班
2. 今天01:00开始摸鱼
3. 验证时间显示正确
4. 调整摸鱼时间
5. 今天01:30结束摸鱼
6. 验证记录保存到昨天，时长30分钟

### 测试场景2：普通摸鱼（回归测试）
1. 设置今天09:00-18:00工作
2. 今天14:00开始摸鱼
3. 验证功能与之前一致
4. 今天14:30结束摸鱼
5. 验证记录保存到今天，时长30分钟

## 总结

新架构通过统一时间计算逻辑，解决了跨日期摸鱼的所有问题：

1. **数据一致性**：所有组件使用相同的时间计算方法
2. **功能完整性**：跨日期摸鱼功能完全正常
3. **向后兼容**：普通摸鱼功能保持不变
4. **代码清晰**：时间计算逻辑集中管理，易于维护

修复后，无论是跨日期摸鱼还是普通摸鱼，所有功能都能正常工作。
