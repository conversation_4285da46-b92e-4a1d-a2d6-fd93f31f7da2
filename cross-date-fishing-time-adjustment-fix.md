# 跨日期摸鱼时间调整功能修复

## 问题描述

用户反馈在跨日期摸鱼时，时间调整功能不可用，始终提示"摸鱼时长不能少于1分钟"。

## 问题根源分析

### 跨日期摸鱼的时间计算问题

在跨日期摸鱼场景中：
```
场景：昨天22:00-今天02:00加班，当前时间今天01:00

摸鱼状态：
- fishingState.startMinutes = 1500 (跨日期时间段中的位置)
- currentMinutes = 60 (今天01:00)

时长计算（错误）：
newDurationMinutes = currentMinutes - newStartMinutes
                   = 60 - (1500 - adjustMinutes)
                   = 60 - 1499 = -1439 (负数！)
```

### 问题的核心

时长计算没有考虑跨日期情况，直接用今天的时间减去跨日期时间段中的位置，导致结果为负数。

## 解决方案

### 1. 添加实际时间计算方法

```javascript
calculateActualCurrentMinutes(fishingState, currentMinutes) {
  const workSegment = fishingState.workSegment
  const startMinutes = fishingState.startMinutes

  // 检查是否为跨日期时间段
  const isCrossDate = workSegment.start >= 1440 || workSegment.end >= 1440

  if (isCrossDate && startMinutes >= 1440) {
    // 跨日期摸鱼：当前时间也需要转换到跨日期时间段中的位置
    return currentMinutes + 1440
  } else {
    // 普通摸鱼：直接使用当前时间
    return currentMinutes
  }
}
```

### 2. 修正时长计算逻辑

#### adjustFishingStartTime方法修正
```javascript
// 修正前（错误）
const newDurationMinutes = currentMinutes - newStartMinutes

// 修正后（正确）
const currentActualMinutes = this.calculateActualCurrentMinutes(fishingState, currentMinutes)
const newDurationMinutes = currentActualMinutes - newStartMinutes
```

#### checkFishingTimeAdjustment方法修正
```javascript
// 修正前（错误）
const newDurationForDecrease = currentMinutes - newStartMinutesForDecrease
const currentDuration = currentMinutes - fishingState.startMinutes

// 修正后（正确）
const currentActualMinutes = this.calculateActualCurrentMinutes(fishingState, currentMinutes)
const newDurationForDecrease = currentActualMinutes - newStartMinutesForDecrease
const currentDuration = currentActualMinutes - fishingState.startMinutes
```

## 修复验证

### 跨日期摸鱼场景测试
```
设置：
- 昨天加班时间段：22:00-次日02:00 (1320-1560分钟)
- 当前时间：今天01:00 (60分钟)
- 摸鱼开始时间：1500分钟

修正后的计算：
1. currentActualMinutes = 60 + 1440 = 1500分钟
2. 当前摸鱼时长 = 1500 - 1500 = 0分钟

时间调整测试：
1. 减少1分钟：
   - newStartMinutes = 1500 + 1 = 1501
   - newDuration = 1500 - 1501 = -1分钟 < 1分钟 → 禁止 ✓

2. 增加1分钟：
   - newStartMinutes = 1500 - 1 = 1499
   - newDuration = 1500 - 1499 = 1分钟 ≥ 1分钟 → 允许 ✓
   - 1499 ≥ 1320 (工作时间段开始) → 允许 ✓
```

### 普通摸鱼场景测试（回归测试）
```
设置：
- 今天工作时间段：09:00-18:00 (540-1080分钟)
- 当前时间：今天14:00 (840分钟)
- 摸鱼开始时间：800分钟

修正后的计算：
1. currentActualMinutes = 840分钟 (无变化)
2. 当前摸鱼时长 = 840 - 800 = 40分钟

时间调整测试：
1. 减少1分钟：
   - newStartMinutes = 800 + 1 = 801
   - newDuration = 840 - 801 = 39分钟 ≥ 1分钟 → 允许 ✓

2. 增加1分钟：
   - newStartMinutes = 800 - 1 = 799
   - newDuration = 840 - 799 = 41分钟 ≥ 1分钟 → 允许 ✓
```

## 关键修改点

### 1. 新增方法
```javascript
// miniprogram/core/managers/fishing-manager.js
calculateActualCurrentMinutes(fishingState, currentMinutes) {
  // 计算当前时间在摸鱼时间段中的实际位置
}
```

### 2. 修正adjustFishingStartTime方法
```javascript
// 使用实际时间位置计算时长
const currentActualMinutes = this.calculateActualCurrentMinutes(fishingState, currentMinutes)
const newDurationMinutes = currentActualMinutes - newStartMinutes
```

### 3. 修正checkFishingTimeAdjustment方法
```javascript
// 使用实际时间位置计算边界条件
const currentActualMinutes = this.calculateActualCurrentMinutes(fishingState, currentMinutes)
const newDurationForDecrease = currentActualMinutes - newStartMinutesForDecrease
const currentDuration = currentActualMinutes - fishingState.startMinutes
```

## 修复效果

### 修复前的问题
```
跨日期摸鱼场景：
- 时长计算：60 - 1500 = -1440分钟 (错误)
- 调整结果：始终提示"摸鱼时长不能少于1分钟"
- 功能状态：完全不可用
```

### 修复后的正确行为
```
跨日期摸鱼场景：
- 时长计算：1500 - 1500 = 0分钟 (正确)
- 调整结果：根据实际情况允许或禁止调整
- 功能状态：正常可用

普通摸鱼场景：
- 时长计算：840 - 800 = 40分钟 (保持不变)
- 调整结果：与之前完全一致
- 功能状态：正常可用
```

## 测试建议

### 测试场景1：跨日期摸鱼刚开始
```
前置条件：
- 昨天22:00-今天02:00加班
- 今天01:00开始摸鱼

测试步骤：
1. 立即尝试减少摸鱼时间
2. 等待1分钟后尝试减少摸鱼时间
3. 尝试增加摸鱼时间

预期结果：
1. 减少按钮禁用（时长不足1分钟）
2. 减少按钮启用（时长超过1分钟）
3. 增加按钮根据边界条件启用/禁用
```

### 测试场景2：跨日期摸鱼进行中
```
前置条件：
- 昨天22:00-今天02:00加班
- 今天00:30开始摸鱼，当前01:00

测试步骤：
1. 尝试增加和减少摸鱼时间
2. 验证按钮状态和调整结果

预期结果：
- 时间调整功能正常工作
- 边界检查正确执行
```

### 测试场景3：普通摸鱼（回归测试）
```
前置条件：
- 今天09:00-18:00工作
- 今天14:00开始摸鱼，当前14:30

测试步骤：
1. 验证时间调整功能
2. 确认与之前行为一致

预期结果：
- 功能完全正常
- 与修复前行为一致
```

## 总结

通过添加`calculateActualCurrentMinutes`方法，我们解决了跨日期摸鱼时间调整功能的核心问题：

1. **正确的时间计算**：考虑跨日期时间段中的实际时间位置
2. **准确的边界检查**：基于正确的时长计算进行边界判断
3. **完整的功能支持**：跨日期摸鱼时间调整功能完全可用
4. **向后兼容**：普通摸鱼功能保持不变

修复后，无论是跨日期摸鱼还是普通摸鱼，时间调整功能都能正常工作。
