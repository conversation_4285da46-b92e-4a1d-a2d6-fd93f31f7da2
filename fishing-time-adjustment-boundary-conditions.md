# 摸鱼时间调整边界条件详解

## 概述

摸鱼时间调整功能需要处理多种边界条件，确保调整后的时间在逻辑上合理且不会造成数据冲突。

## 边界条件详解

### 1. 最小时长限制
**条件**：摸鱼时长不能少于1分钟
**检查逻辑**：`当前时间 - 调整后开始时间 >= 1分钟`
**触发场景**：用户尝试减少摸鱼时间（开始时间延后）
**处理方式**：禁用减号按钮，显示提示"摸鱼时长不能少于1分钟"

```javascript
// 示例：当前时间10:05，摸鱼开始时间10:04
// 尝试减少1分钟 -> 新开始时间10:05
// 时长 = 10:05 - 10:05 = 0分钟 < 1分钟 -> 禁止
```

### 2. 工作时间段限制
**条件**：开始时间不能早于当前工作时间段的开始时间
**检查逻辑**：`调整后开始时间 >= 工作时间段开始时间`
**触发场景**：用户尝试增加摸鱼时间（开始时间提前）
**处理方式**：禁用加号按钮，显示提示"开始时间不能早于工作时间段开始"

```javascript
// 示例：工作时间段9:00-12:00，摸鱼开始时间9:01
// 尝试增加2分钟 -> 新开始时间8:59
// 8:59 < 9:00 -> 禁止
```

### 3. 时间重叠限制（新增）
**条件**：开始时间不能早于上一个摸鱼记录的结束时间
**检查逻辑**：`调整后开始时间 >= 上一个摸鱼记录结束时间`
**触发场景**：用户尝试增加摸鱼时间，但会早于之前的摸鱼时间段结束时间
**处理方式**：禁用加号按钮，显示提示"开始时间不能早于上一个摸鱼时间段的结束时间"

```javascript
// 示例：上一个摸鱼记录9:30-9:45，当前摸鱼开始时间9:50
// 尝试增加6分钟 -> 新开始时间9:44
// 9:44 < 9:45 -> 禁止（早于上一个摸鱼结束时间）
// 尝试增加5分钟 -> 新开始时间9:45
// 9:45 = 9:45 -> 允许（可以紧接着上一个摸鱼）
```

## 实现逻辑

### 边界检查流程
1. **获取当前摸鱼状态**：确认有活跃的摸鱼记录
2. **计算调整后时间**：根据调整分钟数计算新的开始时间
3. **依次检查边界条件**：
   - 最小时长限制
   - 工作时间段限制
   - 时间重叠限制
4. **返回检查结果**：包含是否可以调整和具体原因

### 核心方法

#### fishing-manager.js
```javascript
// 主要调整方法
adjustFishingStartTime(adjustMinutes)

// 边界检查方法
checkFishingTimeAdjustment()

// 获取上一个摸鱼记录结束时间
getPreviousFishingEndTime(currentFishingState)
```

#### data-manager.js
```javascript
// 实现获取上一个摸鱼记录的逻辑
getPreviousFishingEndTime(currentFishingState)

// 设置回调函数
fishingManager.setPreviousFishingEndTimeCallback(callback)
```

## 数据查询逻辑

### 获取上一个摸鱼记录
1. **获取当天摸鱼记录**：根据工作ID和日期获取所有摸鱼记录
2. **筛选有效记录**：只考虑在当前摸鱼开始时间之前结束的记录
3. **找到最近记录**：选择结束时间最接近当前摸鱼开始时间的记录
4. **返回结束时间**：返回该记录的结束时间（分钟格式）

```javascript
// 伪代码示例
function getPreviousFishingEndTime(currentFishingState) {
  const dayFishes = getDayFishes(workId, date)
  let previousEndTime = null
  
  for (const fish of dayFishes) {
    if (fish.end < currentFishingState.startMinutes) {
      if (previousEndTime === null || fish.end > previousEndTime) {
        previousEndTime = fish.end
      }
    }
  }
  
  return previousEndTime
}
```

## 用户体验设计

### 按钮状态管理
- **实时检查**：每秒更新按钮状态（在时间段定时器中）
- **状态同步**：摸鱼状态变化时立即更新按钮状态
- **视觉反馈**：禁用状态有明确的视觉区别

### 错误提示
- **具体原因**：根据不同的边界条件显示相应的提示信息
- **用户友好**：使用通俗易懂的语言描述限制原因
- **操作指导**：在可能的情况下提供解决建议

## 测试场景

### 基础功能测试
1. **正常调整**：在没有边界限制的情况下调整时间
2. **连续调整**：多次点击按钮进行连续调整
3. **状态切换**：在调整过程中切换工作/摸鱼状态

### 边界条件测试
1. **最小时长**：
   - 摸鱼1分钟后尝试减少时间
   - 摸鱼刚开始时尝试减少时间

2. **工作时间段**：
   - 在工作时间段开始时摸鱼并尝试增加时间
   - 在工作时间段中间摸鱼并尝试大幅增加时间

3. **时间重叠**：
   - 有上一个摸鱼记录时尝试增加时间
   - 上一个摸鱼记录紧邻当前摸鱼时的调整

### 数据一致性测试
1. **跨组件同步**：在不同仪表盘间切换时的状态一致性
2. **持久化验证**：调整后重启应用的数据保持
3. **图表更新**：时间图表是否正确反映调整后的状态

## 性能考虑

### 查询优化
- **缓存机制**：避免重复查询当天的摸鱼记录
- **增量更新**：只在必要时重新计算边界条件
- **异步处理**：避免阻塞UI更新

### 内存管理
- **及时清理**：在摸鱼结束时清理相关缓存
- **回调管理**：正确设置和清理回调函数
- **事件监听**：避免内存泄漏

## 扩展性考虑

### 未来功能
- **批量调整**：支持调整多个摸鱼记录
- **智能建议**：根据历史数据提供调整建议
- **冲突解决**：自动处理时间冲突的策略

### 配置选项
- **最小时长设置**：允许用户自定义最小摸鱼时长
- **调整步长**：支持不同的时间调整粒度
- **权限控制**：限制某些情况下的时间调整
