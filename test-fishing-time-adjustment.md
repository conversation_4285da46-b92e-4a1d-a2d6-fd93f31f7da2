# 摸鱼时间调整功能测试

## 功能概述
在摸鱼时间显示前后添加加减按钮，允许用户调整摸鱼开始时间，从而修正摸鱼时长记录。

## 实现的功能

### 1. UI组件
- ✅ 在摸鱼时间显示两侧添加了加减按钮
- ✅ 按钮样式与现有设计保持一致
- ✅ 按钮有禁用状态的视觉反馈

### 2. 核心逻辑
- ✅ 在fishing-manager.js中添加了adjustFishingStartTime方法
- ✅ 在data-manager.js中添加了对外接口adjustFishingTime
- ✅ 实现了边界检查逻辑

### 3. 边界检查
- ✅ 减少时间：确保摸鱼时长不少于1分钟
- ✅ 增加时间：确保开始时间不早于工作时间段开始
- ✅ 时间重叠检查：确保不与上一个摸鱼时间段重叠
- ✅ 按钮禁用状态正确反映边界条件

### 4. 事件处理
- ✅ fishing-control组件触发fishingtimeadjusted事件
- ✅ dashboard2组件监听并处理时间调整事件
- ✅ dashboard1组件添加了处理方法（通过selectComponent访问）

## 测试场景

### 场景1：正常调整
1. 开始摸鱼
2. 等待几分钟
3. 点击加号按钮增加时长
4. 点击减号按钮减少时长
5. 验证时间显示和图表更新

### 场景2：边界测试
1. 开始摸鱼后立即尝试减少时间（应该禁用）
2. 在工作时间段开始时摸鱼，尝试增加时间（应该禁用）
3. 在有上一个摸鱼记录的情况下，尝试增加时间到重叠（应该禁用）
4. 验证按钮禁用状态

### 场景3：数据一致性
1. 调整摸鱼时间后结束摸鱼
2. 检查保存的摸鱼记录是否使用调整后的时间
3. 验证时间图表显示正确

## 文件修改清单

### 核心逻辑文件
1. `miniprogram/core/managers/fishing-manager.js`
   - 添加adjustFishingStartTime方法
   - 添加checkFishingTimeAdjustment方法

2. `miniprogram/core/managers/data-manager.js`
   - 添加adjustFishingTime对外接口
   - 添加checkFishingTimeAdjustment接口

### fishing-control组件文件（仪表盘2使用）
3. `miniprogram/components/fishing-control/index.wxml`
   - 添加加减按钮HTML结构

4. `miniprogram/components/fishing-control/index.wxss`
   - 添加按钮样式定义

5. `miniprogram/components/fishing-control/index.js`
   - 添加按钮状态管理
   - 添加事件处理方法
   - 添加时间调整逻辑

### dashboard1组件文件（仪表盘1）
6. `miniprogram/components/dashboard1/index.wxml`
   - 在时间段时长显示区域添加加减按钮
   - 只在摸鱼状态下显示按钮

7. `miniprogram/components/dashboard1/index.wxss`
   - 添加时间调整按钮样式
   - 适配不同状态的颜色主题

8. `miniprogram/components/dashboard1/index.js`
   - 添加按钮状态数据字段
   - 添加updateFishingTimeAdjustButtons方法
   - 添加按钮点击事件处理方法
   - 在时间更新时同步按钮状态

### dashboard2组件文件（仪表盘2）
9. `miniprogram/components/dashboard2/index.wxml`
   - 添加fishingtimeadjusted事件绑定

10. `miniprogram/components/dashboard2/index.js`
    - 添加onFishingTimeAdjusted事件处理

## 预期效果

### 用户体验
- 用户可以方便地调整摸鱼时间，解决忘记及时点击开始摸鱼的问题
- 按钮状态清晰反映是否可以调整
- 调整后立即看到时间变化和图表更新

### 数据准确性
- 调整后的时间会正确保存到摸鱼记录中
- 时间图表会实时反映调整后的状态
- 收入计算基于调整后的时间

### 边界安全
- 不允许调整到不合理的时间范围
- 防止数据不一致的情况发生
