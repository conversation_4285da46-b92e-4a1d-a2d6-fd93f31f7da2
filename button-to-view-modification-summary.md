# 摸鱼时间调整按钮：从button改为view元素

## 修改概述

根据用户要求，将摸鱼时间调整的加减按钮从`button`元素改为`view`元素实现，保持相同的功能和视觉效果。

## 修改原因

- 更好的样式控制：view元素没有浏览器默认样式，更容易自定义
- 统一的交互体验：避免button元素的默认行为
- 更灵活的布局：view元素在布局上更加灵活

## 修改的文件

### 1. dashboard1组件（仪表盘1）

#### miniprogram/components/dashboard1/index.wxml
```xml
<!-- 修改前 -->
<button class="time-adjust-btn" disabled="{{!canIncrease}}" bindtap="onIncrease">

<!-- 修改后 -->
<view class="time-adjust-btn" bindtap="onIncrease">
```

#### miniprogram/components/dashboard1/index.wxss
- 移除了`padding: 0; margin: 0;`（button特有属性）
- 添加了`cursor: pointer; user-select: none;`
- 添加了`:hover`伪类样式
- 为禁用状态添加了`cursor: not-allowed;`
- 为图标添加了`pointer-events: none;`

#### miniprogram/components/dashboard1/index.js
- 在事件处理函数中添加了`e.stopPropagation()`阻止事件冒泡
- 添加了禁用状态的用户提示
- 移除了对`disabled`属性的依赖，改为在JavaScript中检查状态

### 2. fishing-control组件（仪表盘2）

#### miniprogram/components/fishing-control/index.wxml
```xml
<!-- 修改前 -->
<button class="time-adjust-btn" disabled="{{!canDecrease}}" size="mini">

<!-- 修改后 -->
<view class="time-adjust-btn {{!canDecrease ? 'disabled' : ''}}">
```

#### miniprogram/components/fishing-control/index.wxss
- 与dashboard1相同的样式修改
- 保持原有的蓝色主题色彩

#### miniprogram/components/fishing-control/index.js
- 与dashboard1相同的事件处理修改

## 主要变化

### HTML结构变化
```xml
<!-- 修改前 -->
<button 
  class="time-adjust-btn decrease-btn {{!canDecrease ? 'disabled' : ''}}" 
  disabled="{{!canDecrease}}"
  bindtap="onDecreaseFishingTime"
  size="mini"
>
  <text class="adjust-icon">−</text>
</button>

<!-- 修改后 -->
<view 
  class="time-adjust-btn decrease-btn {{!canDecrease ? 'disabled' : ''}}" 
  bindtap="onDecreaseFishingTime"
>
  <text class="adjust-icon">−</text>
</view>
```

### CSS样式变化
```css
/* 修改前 */
.time-adjust-btn {
  padding: 0;
  margin: 0;
  /* button特有样式 */
}

/* 修改后 */
.time-adjust-btn {
  cursor: pointer;
  user-select: none;
  /* view元素适用样式 */
}

/* 新增hover效果 */
.time-adjust-btn:not(.disabled):hover {
  background: rgba(245, 158, 11, 0.05);
}

/* 禁用状态光标 */
.time-adjust-btn.disabled {
  cursor: not-allowed;
}

/* 防止图标被选中 */
.adjust-icon {
  pointer-events: none;
}
```

### JavaScript逻辑变化
```javascript
// 修改前
onIncreaseFishingTime() {
  if (!this.data.canIncrease) {
    return
  }
  this.adjustFishingTime(1)
}

// 修改后
onIncreaseFishingTime(e) {
  // 阻止事件冒泡
  if (e && e.stopPropagation) {
    e.stopPropagation()
  }

  if (!this.data.canIncrease) {
    wx.showToast({
      title: '无法增加摸鱼时间',
      icon: 'none',
      duration: 1500
    })
    return
  }

  this.adjustFishingTime(1)
}
```

## 功能保持

### 保持不变的功能
1. **视觉效果**：按钮的外观和动画效果完全一致
2. **交互逻辑**：点击行为和状态管理逻辑不变
3. **边界检查**：所有边界条件检查逻辑保持不变
4. **状态同步**：按钮启用/禁用状态的同步机制不变

### 改进的功能
1. **用户反馈**：禁用状态下点击会显示提示信息
2. **事件处理**：添加了事件冒泡阻止，避免意外触发
3. **样式控制**：更精确的样式控制，更好的hover效果
4. **可访问性**：更清晰的光标状态指示

## 兼容性说明

### 微信小程序兼容性
- view元素是微信小程序的基础组件，兼容性最好
- 所有样式属性都是标准CSS，无兼容性问题
- 事件处理使用标准的bindtap，完全兼容

### 功能兼容性
- 与现有的摸鱼时间调整逻辑完全兼容
- 不影响数据管理和状态同步
- 不影响其他组件的功能

## 测试建议

### 视觉测试
1. 确认按钮外观与之前一致
2. 测试hover效果（在支持的设备上）
3. 验证禁用状态的视觉反馈

### 功能测试
1. 测试点击增加/减少摸鱼时间
2. 验证边界条件下的按钮禁用
3. 确认禁用状态下的提示信息

### 交互测试
1. 测试事件冒泡是否被正确阻止
2. 验证在不同设备上的触摸响应
3. 确认长按等操作的行为

## 总结

通过将button元素改为view元素，我们获得了更好的样式控制能力和更一致的用户体验，同时保持了所有原有功能。这个修改提升了代码的可维护性和用户体验的一致性。
