# 摸鱼时间调整功能实现说明

## 功能概述

实现了在摸鱼时间显示前后添加加减按钮的功能，用户可以通过点击按钮来调整摸鱼开始时间，从而修正摸鱼时长记录。这解决了用户可能在开始摸鱼几分钟后才想起点击开始按钮的场景。

## 核心特性

### 1. 用户界面
- **加减按钮**：在摸鱼时间显示的两侧添加了 "−" 和 "+" 按钮
- **视觉反馈**：按钮有正常、禁用两种状态，禁用时显示灰色
- **响应式设计**：按钮大小和样式与现有设计保持一致

### 2. 功能逻辑
- **时间调整**：每次点击调整1分钟
- **实时更新**：调整后立即更新摸鱼时间显示
- **数据同步**：调整后的时间会保存到localStorage并同步到UI

### 3. 边界保护
- **最小时长限制**：摸鱼时长不能少于1分钟
- **工作时间限制**：开始时间不能早于当前工作时间段的开始时间
- **时间重叠限制**：开始时间不能早于上一个摸鱼时间段的结束时间
- **按钮状态管理**：根据边界条件自动启用/禁用按钮

## 技术实现

### 核心方法

#### fishing-manager.js
```javascript
// 调整摸鱼开始时间
adjustFishingStartTime(adjustMinutes)

// 检查调整边界条件
checkFishingTimeAdjustment()

// 获取上一个摸鱼记录结束时间
getPreviousFishingEndTime(currentFishingState)

// 设置获取上一个摸鱼记录的回调
setPreviousFishingEndTimeCallback(callback)
```

#### data-manager.js
```javascript
// 对外接口
adjustFishingTime(adjustMinutes)
checkFishingTimeAdjustment()

// 获取上一个摸鱼记录结束时间的实现
getPreviousFishingEndTime(currentFishingState)
```

#### fishing-control组件
```javascript
// 增加时间（开始时间提前）
onIncreaseFishingTime()

// 减少时间（开始时间延后）
onDecreaseFishingTime()

// 更新按钮状态
updateTimeAdjustButtons()
```

### 事件流程

1. **用户点击按钮** → fishing-control组件处理点击事件
2. **调用数据管理器** → 执行时间调整逻辑和边界检查
3. **更新本地状态** → 保存调整后的摸鱼状态到localStorage
4. **触发UI更新** → 更新时间显示和按钮状态
5. **通知父组件** → 触发fishingtimeadjusted事件
6. **更新图表** → 父组件刷新时间图表显示

## 使用场景

### 场景1：忘记及时开始摸鱼
1. 用户在10:05开始摸鱼，但实际从10:00就开始了
2. 点击 "+" 按钮5次，将开始时间调整为10:00
3. 摸鱼时长从当前显示增加5分钟

### 场景2：误操作提前开始
1. 用户在9:58点击开始摸鱼，但实际10:00才开始
2. 点击 "−" 按钮2次，将开始时间调整为10:00
3. 摸鱼时长相应减少2分钟

## 边界情况处理

### 减少时间的限制
- **条件**：当前时间 - 调整后开始时间 >= 1分钟
- **提示**：摸鱼时长不能少于1分钟
- **按钮状态**：不满足条件时禁用减号按钮

### 增加时间的限制
- **条件1**：调整后开始时间 >= 工作时间段开始时间
- **条件2**：调整后开始时间 >= 上一个摸鱼记录结束时间
- **提示**：开始时间不能早于工作时间段开始 / 开始时间不能早于上一个摸鱼时间段的结束时间
- **按钮状态**：不满足任一条件时禁用加号按钮

## 数据一致性

### 状态同步
- **摸鱼状态**：startTime和startMinutes同步更新
- **本地存储**：调整后立即保存到localStorage
- **UI显示**：实时更新摸鱼时长显示

### 图表更新
- **时间图表**：通过事件通知机制更新
- **收入计算**：基于调整后的时间重新计算
- **统计数据**：确保数据的准确性

## 用户体验优化

### 操作反馈
- **成功提示**：显示"摸鱼时间已增加/减少X分钟"
- **错误提示**：显示具体的限制原因
- **按钮动画**：点击时有缩放效果

### 视觉设计
- **按钮样式**：圆形按钮，蓝色边框
- **图标设计**：使用 "−" 和 "+" 符号
- **禁用状态**：灰色显示，降低透明度

## 兼容性说明

### 组件支持
- **dashboard2**：直接支持，已添加事件监听
- **dashboard1**：通过selectComponent间接支持
- **其他页面**：可通过fishing-control组件使用

### 数据格式
- **向后兼容**：不影响现有摸鱼记录格式
- **时间精度**：保持分钟级精度
- **存储结构**：复用现有的摸鱼状态结构

## 测试建议

### 功能测试
1. 正常调整时间并验证显示
2. 测试边界条件的按钮禁用
3. 验证调整后的数据保存
4. 检查时间图表的更新

### 边界测试
1. 摸鱼时长接近1分钟时的减少操作
2. 开始时间接近工作时间段开始时的增加操作
3. 连续快速点击按钮的响应

### 数据一致性测试
1. 调整时间后结束摸鱼，检查保存的记录
2. 跨页面切换后的状态保持
3. 应用重启后的状态恢复
