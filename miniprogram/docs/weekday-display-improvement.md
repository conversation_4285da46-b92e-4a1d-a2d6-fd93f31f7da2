# 日期显示增加周几信息

## 改进内容

在设置日期安排模态框的顶部日期显示中添加周几信息，格式为：`YYYY年MM月DD日 - 周X`

### 示例效果
- 改进前：`2024年1月15日`
- 改进后：`2024年1月15日 - 周一`

## 技术实现

### 1. 在utils中新增周几相关函数

在 `miniprogram/utils/time-utils.js` 中添加了三个新函数：

```javascript
/**
 * 获取日期对应的星期几
 * @param {Date} date - 日期对象
 * @returns {string} 星期几的中文表示
 */
export function getWeekday(date) {
  if (!date || !(date instanceof Date)) {
    return '未知'
  }
  
  const weekdays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六']
  return weekdays[date.getDay()]
}

/**
 * 格式化日期为 YYYY年MM月DD日 - 周X 格式
 * @param {Date} date - 日期对象
 * @returns {string} 格式化后的日期字符串，包含星期几
 */
export function formatDateWithWeekday(date) {
  if (!date || !(date instanceof Date)) {
    return '未知日期'
  }
  
  const dateStr = formatDate(date)
  const weekday = getWeekday(date)
  
  return `${dateStr} - ${weekday}`
}
```

### 2. 在组件中使用新函数

在 `miniprogram/components/schedule-modal/index.js` 中：

```javascript
// 导入新的格式化函数
import { formatDateWithWeekday } from '../../utils/time-utils.js'

// 修改格式化日期文本方法
_formatDateText(date) {
  return formatDateWithWeekday(date)
}
```

## 设计考虑

### 周几映射
使用JavaScript的 `Date.getDay()` 方法：
- 0 → 周日
- 1 → 周一
- 2 → 周二
- 3 → 周三
- 4 → 周四
- 5 → 周五
- 6 → 周六

### 格式选择
选择 `周X` 而不是 `星期X` 的原因：
1. **简洁性**：`周X` 比 `星期X` 更简洁
2. **一致性**：与现代应用的常见表示方式一致
3. **可读性**：在有限的空间内更容易阅读

### 错误处理
- 当日期对象无效时，返回 `未知日期`
- 当日期为null或undefined时，返回 `未知`
- 保持原有的错误处理逻辑

## 代码复用性

### 函数设计
1. **getWeekday(date)**：单一职责，只获取周几
2. **formatDateWithWeekday(date)**：组合函数，包含日期和周几
3. **保持向后兼容**：原有的 `formatDate()` 函数保持不变

### 使用场景
这些函数可以在其他需要显示周几的地方复用：
- 日历页面
- 统计页面
- 其他模态框
- 报表生成

## 用户体验提升

### 信息完整性
- 用户可以快速了解选择日期是星期几
- 有助于用户进行工作安排的规划
- 减少用户心理计算负担

### 视觉效果
- 日期信息更加完整
- 符合用户对日期显示的期望
- 与其他应用的显示习惯一致

## 测试验证

### 功能测试
- [ ] 周一到周日的显示正确
- [ ] 跨月、跨年日期的周几计算正确
- [ ] 特殊日期（如闰年2月29日）的处理正确

### 边界测试
- [ ] 无效日期的错误处理
- [ ] null/undefined参数的处理
- [ ] 极端日期值的处理

### 集成测试
- [ ] 模态框中的日期显示正确
- [ ] 不同日期选择时的动态更新
- [ ] 与其他组件的兼容性

## 示例效果

```
设置日期安排
2024年1月15日 - 周一

日期状态: 工作 ›

日期安排:
...
```

## 后续扩展建议

1. **国际化支持**：可以考虑支持英文等其他语言的周几显示
2. **农历支持**：可以考虑添加农历日期显示
3. **节假日标识**：可以考虑在特殊节假日时添加标识
4. **工作日标识**：可以考虑区分工作日和周末的显示样式

## 相关文件

- `miniprogram/utils/time-utils.js` - 新增周几相关函数
- `miniprogram/components/schedule-modal/index.js` - 使用新的格式化函数
- `miniprogram/components/schedule-modal/index.wxml` - 显示格式化后的日期文本
