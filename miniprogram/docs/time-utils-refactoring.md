# 时间工具函数重构

## 重构目标

将设置工作计划模态框组件中的时间相关方法重构为使用 `miniprogram/utils/time-utils.js` 中已有的工具函数，提高代码复用性和一致性。

## 重构内容

### 1. 导入utils中的时间工具函数

```javascript
import { 
  formatDateWithWeekday, 
  formatDuration, 
  parseTimeToMinutes, 
  minutesToTimeString 
} from '../../utils/time-utils.js'
```

### 2. 重构的方法

#### 2.1 `_formatHours` 方法

**重构前**：
```javascript
_formatHours(minutes) {
  const hours = Math.floor(minutes / 60)
  const mins = minutes % 60
  
  if (hours > 0 && mins > 0) {
    return `${hours}小时${mins}分钟`
  } else if (hours > 0) {
    return `${hours}小时`
  } else if (mins > 0) {
    return `${mins}分钟`
  } else {
    return '0小时'
  }
}
```

**重构后**：
```javascript
_formatHours(minutes) {
  return formatDuration(minutes)
}
```

**优势**：
- 代码更简洁
- 使用utils中经过充分测试的方法
- 边界处理更完善（utils版本处理了负数和小数）

#### 2.2 `_timeToMinutes` 方法

**重构前**：
```javascript
_timeToMinutes(timeStr, isNextDay = false) {
  if (!timeStr) return 0
  
  const [hours, minutes] = timeStr.split(':').map(Number)
  let totalMinutes = hours * 60 + minutes
  
  if (isNextDay) {
    totalMinutes += 24 * 60
  }
  
  return totalMinutes
}
```

**重构后**：
```javascript
_timeToMinutes(timeStr, isNextDay = false) {
  let totalMinutes = parseTimeToMinutes(timeStr)
  
  if (isNextDay) {
    totalMinutes += 24 * 60 // 加上24小时
  }
  
  return totalMinutes
}
```

**优势**：
- 基于utils中的基础方法
- 保留了组件特有的跨日逻辑
- 更好的错误处理（utils版本有类型检查）

#### 2.3 `_minutesToTimeDisplay` 方法

**重构前**：
```javascript
_minutesToTimeDisplay(minutes) {
  const totalMinutes = Math.abs(minutes)
  const hours = Math.floor(totalMinutes / 60)
  const mins = totalMinutes % 60
  
  let timeStr = `${String(hours % 24).padStart(2, '0')}:${String(mins).padStart(2, '0')}`
  
  if (minutes >= 24 * 60) {
    timeStr += ' 次日'
  }
  
  return timeStr
}
```

**重构后**：
```javascript
_minutesToTimeDisplay(minutes) {
  const totalMinutes = Math.abs(minutes)
  const hours = Math.floor(totalMinutes / 60)
  
  // 使用utils中的方法生成基础时间字符串
  let timeStr = minutesToTimeString(totalMinutes % (24 * 60))
  
  // 如果超过24小时，添加次日标识
  if (minutes >= 24 * 60) {
    timeStr += ' 次日'
  }
  
  return timeStr
}
```

**优势**：
- 基于utils中的标准时间格式化方法
- 保留了组件特有的跨日显示逻辑
- 代码更清晰，职责分离

## 保留的方法

### `_calculateInputDuration` 方法

这个方法处理了组件特定的输入格式和业务逻辑，暂时保留：

```javascript
_calculateInputDuration(input) {
  const startTime = input.startTime
  const endTime = input.endTime
  
  if (!startTime || !endTime) return 0
  
  const startMinutes = this._timeToMinutes(startTime, input.isStartNextDay)
  const endMinutes = this._timeToMinutes(endTime, input.isEndNextDay)
  
  let duration = endMinutes - startMinutes
  
  // 处理跨日情况
  if (duration <= 0) {
    duration += 24 * 60
  }
  
  return Math.max(0, duration)
}
```

**保留原因**：
- 处理组件特定的输入对象格式
- 包含复杂的跨日逻辑
- 与组件的数据结构紧密耦合

## 重构效果

### 代码质量提升
1. **代码复用**：减少重复代码，使用经过测试的工具函数
2. **一致性**：时间格式化在整个应用中保持一致
3. **可维护性**：集中管理时间相关逻辑，便于维护和更新

### 功能保持
1. **向后兼容**：所有现有功能保持不变
2. **跨日支持**：保留了组件特有的跨日处理逻辑
3. **错误处理**：继承了utils中更完善的错误处理

### 性能优化
1. **代码体积**：减少了重复的时间处理代码
2. **执行效率**：使用优化过的工具函数

## 测试验证

### 功能测试
- [ ] 时间段持续时间计算正确
- [ ] 时间格式显示正确
- [ ] 跨日时间处理正确
- [ ] 边界情况处理正确

### 兼容性测试
- [ ] 现有数据格式兼容
- [ ] 用户界面显示正常
- [ ] 计算结果准确

## 后续优化建议

1. **进一步抽象**：可以考虑将 `_calculateInputDuration` 也抽象到utils中
2. **类型安全**：考虑使用TypeScript增强类型安全
3. **单元测试**：为重构后的方法添加单元测试
4. **文档完善**：更新相关的API文档

## 相关文件

- `miniprogram/utils/time-utils.js` - 时间工具函数库
- `miniprogram/components/schedule-modal/index.js` - 重构的组件文件
- `miniprogram/docs/time-utils-refactoring.md` - 本重构文档
