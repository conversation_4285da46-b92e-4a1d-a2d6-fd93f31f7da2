# 小程序更新管理器使用指南

## 功能概述

本项目已集成小程序启动时自动检查更新功能，基于微信小程序官方的 `wx.getUpdateManager()` API 实现。

## 功能特性

- **启动时自动检查**：每次小程序启动时自动检查是否有新版本
- **异步下载**：新版本在后台异步下载，不阻塞当前使用
- **用户友好提示**：新版本准备好后显示简洁的更新提示
- **一键更新**：只显示确定按钮，点击即可应用更新并重启
- **错误处理**：更新失败时显示友好提示，不影响正常使用

## 实现位置

更新检查功能在 `miniprogram/app.js` 文件中实现：

- `onLaunch()` 方法中调用 `checkForUpdate()`
- `checkForUpdate()` 方法实现完整的更新逻辑

## 更新流程

1. **应用启动**：用户打开小程序
2. **检查更新**：自动检查是否有新版本
3. **后台下载**：如果有新版本，在后台异步下载
4. **提示用户**：下载完成后显示更新提示框
5. **应用更新**：用户点击确定后应用新版本并重启

## 用户体验

### 更新提示框
- **标题**：更新提示
- **内容**：新版本已经准备好，点击确定重启应用更新到最新版本
- **按钮**：只有一个"确定"按钮
- **行为**：点击确定后立即应用更新并重启

### 错误处理
- 如果更新下载失败，会显示 Toast 提示："更新失败，请稍后重试"
- 更新检查失败不会影响应用正常启动

## 技术实现

### 核心 API
```javascript
const updateManager = wx.getUpdateManager()
```

### 事件监听
1. `onCheckForUpdate`：监听检查更新结果
2. `onUpdateReady`：监听新版本准备完成
3. `onUpdateFailed`：监听更新失败

### 关键代码
```javascript
// 检查更新
updateManager.onCheckForUpdate(function (res) {
  console.log('更新检查完成，是否有新版本:', res.hasUpdate)
})

// 新版本准备好
updateManager.onUpdateReady(function () {
  wx.showModal({
    title: '更新提示',
    content: '新版本已经准备好，点击确定重启应用更新到最新版本',
    showCancel: false,
    confirmText: '确定',
    success(res) {
      if (res.confirm) {
        updateManager.applyUpdate()
      }
    }
  })
})
```

## 测试方法

### 开发环境测试
1. 运行测试文件：`miniprogram/test/update-manager-test.js`
2. 查看控制台输出，验证更新流程

### 真机测试
1. 发布新版本到微信小程序后台
2. 在真机上打开小程序
3. 观察是否出现更新提示

## 注意事项

1. **基础库版本**：需要微信基础库 1.9.90 及以上版本
2. **网络环境**：更新检查需要网络连接
3. **发布时间**：新版本发布后，通常需要 24 小时才能覆盖 99% 以上用户
4. **开发工具**：在微信开发者工具中无法完全模拟真实的更新流程

## 相关文档

- [微信小程序更新机制官方文档](https://developers.weixin.qq.com/miniprogram/dev/api/base/update/wx.getUpdateManager.html)
- [小程序版本管理](https://developers.weixin.qq.com/miniprogram/dev/framework/runtime/update-mechanism.html)

## 维护说明

如需修改更新提示的文案或行为，请编辑 `miniprogram/app.js` 文件中的 `checkForUpdate()` 方法。
