// 设置工作计划模态框组件
import {
  formatDateWithWeekday,
  formatDuration,
  parseTimeToMinutes,
  minutesToTimeString
} from '../../utils/time-utils.js'

Component({
  /**
   * 组件的属性列表
   */
  properties: {
    // 是否显示
    visible: {
      type: Boolean,
      value: false
    },
    // 选中的日期（ISO字符串格式）
    selectedDate: {
      type: String,
      value: ''
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    // 工作计划表单
    dailyIncome: 0,
    timeInputs: [],
    typeOptions: [
      { value: 'work', text: '工作', icon: '💼' },
      { value: 'rest', text: '休息', icon: '☕' },
      { value: 'overtime', text: '加班', icon: '🌙' }
    ],

    // 总收入和冲突检测
    totalIncome: 0,
    totalIncomeText: '0.00',
    hasTimeConflict: false,

    // 时间统计
    workHours: 0,
    workHoursText: '0小时',
    restHours: 0,
    restHoursText: '0小时',
    overtimeHours: 0,
    overtimeHoursText: '0小时',
    
    // 日期状态相关
    dateStatus: 'work',
    statusOptions: [],
    statusIndex: 0,
    selectedStatusConfig: null,
    
    // 时间段选择器相关
    editingTimeIndex: -1, // 正在编辑的时间段索引

    // 子组件状态管理
    showDateTypeSelector: false,
    showSmartIncomeModal: false,
    showScheduleImportModal: false,
    showTimeRangePicker: false,

    // 时间范围选择器相关
    timeRangeStartTime: '09:00',
    timeRangeEndTime: '18:00',
    timeRangeIsStartNextDay: false,
    timeRangeIsEndNextDay: false,

    // 模态框动画状态
    modalVisible: false,

    // 组件内部数据
    selectedDateText: '',
    currentWorkId: ''
  },

  /**
   * 组件的方法列表
   */
  methods: {
    /**
     * 监听属性变化
     */
    _onPropertiesChange() {
      if (this.data.visible && this.properties.selectedDate) {
        // 确保服务可用后再初始化模态框
        this._ensureServicesAndInitialize()
      } else {
        this.setData({ modalVisible: false })
      }
    },

    /**
     * 确保服务可用并初始化
     */
    _ensureServicesAndInitialize() {
      // 直接获取服务并初始化
      this._initializeServices()
      this._initializeModal()
    },

    /**
     * 初始化模态框
     */
    _initializeModal() {
      const selectedDateStr = this.properties.selectedDate
      console.log('设置工作计划组件：初始化 - selectedDate 字符串:', selectedDateStr)

      if (!selectedDateStr) {
        console.warn('设置工作计划组件：selectedDate 未提供')
        return
      }

      // 将字符串转换为 Date 对象
      const selectedDate = new Date(selectedDateStr)

      // 检查日期是否有效
      if (isNaN(selectedDate.getTime())) {
        console.warn('设置工作计划组件：selectedDate 无效:', selectedDateStr)
        return
      }

      // 格式化日期文本
      const selectedDateText = formatDateWithWeekday(selectedDate)

      console.log('设置工作计划组件：初始化 - 转换后的 selectedDate:', selectedDate)

      // 获取日期数据
      const dayData = this.timeSegmentService.getDayData(selectedDate)

      // 获取状态选项
      const statusOptions = this.timeSegmentService.getStatusOptions()
      console.log('设置工作计划组件：状态选项:', statusOptions)

      // 获取当前状态
      const currentStatus = this.timeSegmentService.getDateStatus(selectedDate, this.data.currentWorkId)
      const defaultStatus = statusOptions.length > 0 ? statusOptions[0].value : 'work'
      const statusIndex = statusOptions.findIndex(option => option.value === currentStatus)
      const defaultStatusIndex = statusIndex >= 0 ? statusIndex : 0
      const selectedStatusConfig = statusOptions[defaultStatusIndex] || statusOptions[0]

      console.log('设置工作计划组件：状态配置:', {
        currentStatus,
        statusIndex,
        defaultStatusIndex,
        selectedStatusConfig
      })

      // 创建默认时间段并计算合理的收入分配
      const targetDailyIncome = dayData.dailyIncome || 500
      const defaultTimeInput = this._createDefaultTimeInputsWithIncome(targetDailyIncome)

      this.setData({
        selectedDateText,
        dailyIncome: dayData.dailyIncome || 500,
        dateStatus: dayData.segments.length > 0 ? currentStatus : defaultStatus,
        statusIndex: dayData.segments.length > 0 ? (statusIndex >= 0 ? statusIndex : defaultStatusIndex) : defaultStatusIndex,
        selectedStatusConfig: dayData.segments.length > 0 ? selectedStatusConfig : statusOptions[defaultStatusIndex],
        statusOptions,
        timeInputs: dayData.segments.length > 0 ?
          dayData.segments.map(segment => ({
            startTime: this._minutesToTimeDisplay(segment.start).replace('次日', ''),
            endTime: this._minutesToTimeDisplay(segment.end).replace('次日', ''),
            type: segment.type,
            typeIndex: this.data.typeOptions.findIndex(option => option.value === segment.type),
            income: segment.income || 0,
            incomeText: (segment.income || 0).toString(),
            hourlyRate: 0,
            hourlyRateText: '',
            isStartNextDay: segment.start >= 24 * 60,
            isEndNextDay: segment.end >= 24 * 60,
            _isEditingHourlyRate: false
          })) : defaultTimeInput
      }, () => {
        // 初始化显示数据
        this._updateAllCalculations()

        // 延迟显示动画
        setTimeout(() => {
          this.setData({ modalVisible: true })
        }, 50)
      })
    },

    /**
     * 分钟转时间显示（基于utils中的minutesToTimeString，支持跨日显示）
     */
    _minutesToTimeDisplay(minutes) {
      const totalMinutes = Math.abs(minutes)
      const hours = Math.floor(totalMinutes / 60)

      // 使用utils中的方法生成基础时间字符串
      let timeStr = minutesToTimeString(totalMinutes % (24 * 60))

      // 如果超过24小时，添加次日标识
      if (minutes >= 24 * 60) {
        timeStr += ' 次日'
      }

      return timeStr
    },

    /**
     * 更新所有计算
     */
    _updateAllCalculations() {
      this._updateTimeStatistics()
      this._updateTotalIncome()
      this._updateHourlyRates()
      this._updateDurationTexts()
      this._checkTimeConflicts()
    },

    /**
     * 更新时间统计
     */
    _updateTimeStatistics() {
      let workMinutes = 0
      let restMinutes = 0
      let overtimeMinutes = 0

      // 添加防护性检查
      if (!this.data.timeInputs || !Array.isArray(this.data.timeInputs)) {
        console.warn('组件：timeInputs 数据无效', this.data.timeInputs)
        this.setData({
          workHours: 0,
          workHoursText: '0小时',
          restHours: 0,
          restHoursText: '0小时',
          overtimeHours: 0,
          overtimeHoursText: '0小时'
        })
        return
      }

      this.data.timeInputs.forEach(input => {
        if (!input) return

        const duration = this._calculateInputDuration(input)

        switch (input.type) {
          case 'work':
            workMinutes += duration
            break
          case 'rest':
            restMinutes += duration
            break
          case 'overtime':
            overtimeMinutes += duration
            break
        }
      })

      this.setData({
        workHours: workMinutes / 60,
        workHoursText: formatDuration(workMinutes),
        restHours: restMinutes / 60,
        restHoursText: formatDuration(restMinutes),
        overtimeHours: overtimeMinutes / 60,
        overtimeHoursText: formatDuration(overtimeMinutes)
      })
    },

    /**
     * 计算时间段持续时间（分钟）
     */
    _calculateInputDuration(input) {
      const startTime = input.startTime
      const endTime = input.endTime
      
      if (!startTime || !endTime) return 0
      
      const [startHour, startMinute] = startTime.split(':').map(Number)
      const [endHour, endMinute] = endTime.split(':').map(Number)
      
      let startMinutes = startHour * 60 + startMinute
      let endMinutes = endHour * 60 + endMinute
      
      // 处理跨日情况
      if (input.isStartNextDay) {
        startMinutes += 24 * 60
      }
      if (input.isEndNextDay) {
        endMinutes += 24 * 60
      }
      
      // 如果结束时间小于开始时间，说明跨日了
      if (endMinutes <= startMinutes && !input.isEndNextDay) {
        endMinutes += 24 * 60
      }
      
      return Math.max(0, endMinutes - startMinutes)
    },

    /**
     * 更新总收入
     */
    _updateTotalIncome() {
      // 添加防护性检查
      if (!this.data.timeInputs || !Array.isArray(this.data.timeInputs)) {
        this.setData({
          totalIncome: 0,
          totalIncomeText: '0.00'
        })
        return
      }

      const totalIncome = this.data.timeInputs.reduce((sum, input) => {
        if (!input) return sum
        return sum + (parseFloat(input.income) || 0)
      }, 0)

      this.setData({
        totalIncome,
        totalIncomeText: totalIncome.toFixed(2)
      })
    },

    /**
     * 更新时薪
     */
    _updateHourlyRates() {
      const timeInputs = this.data.timeInputs.map(input => {
        if (input._isEditingHourlyRate) {
          return input // 如果正在编辑时薪，不更新
        }

        const duration = this._calculateInputDuration(input)
        const income = parseFloat(input.income) || 0
        const hourlyRate = duration > 0 ? income / (duration / 60) : 0

        return {
          ...input,
          hourlyRate,
          hourlyRateText: hourlyRate.toFixed(2)
        }
      })

      this.setData({ timeInputs })
    },

    /**
     * 更新时长文本显示
     */
    _updateDurationTexts() {
      const timeInputs = this.data.timeInputs.map(input => {
        const duration = this._calculateInputDuration(input)
        const durationText = formatDuration(duration)

        return {
          ...input,
          durationText
        }
      })

      this.setData({ timeInputs })
    },

    /**
     * 检查时间冲突
     */
    _checkTimeConflicts() {
      const hasConflict = this._hasTimeConflicts(this.data.timeInputs)
      this.setData({ hasTimeConflict: hasConflict })
    },

    /**
     * 检查是否有时间冲突
     */
    _hasTimeConflicts(timeInputs) {
      for (let i = 0; i < timeInputs.length; i++) {
        for (let j = i + 1; j < timeInputs.length; j++) {
          if (this._isTimeOverlap(timeInputs[i], timeInputs[j])) {
            return true
          }
        }
      }
      return false
    },

    /**
     * 检查两个时间段是否重叠
     */
    _isTimeOverlap(input1, input2) {
      const start1 = this._timeToMinutes(input1.startTime, input1.isStartNextDay)
      const end1 = this._timeToMinutes(input1.endTime, input1.isEndNextDay)
      const start2 = this._timeToMinutes(input2.startTime, input2.isStartNextDay)
      const end2 = this._timeToMinutes(input2.endTime, input2.isEndNextDay)

      return start1 < end2 && start2 < end1
    },

    /**
     * 时间转分钟（基于utils中的parseTimeToMinutes，支持跨日）
     */
    _timeToMinutes(timeStr, isNextDay = false) {
      let totalMinutes = parseTimeToMinutes(timeStr)

      if (isNextDay) {
        totalMinutes += 24 * 60 // 加上24小时
      }

      return totalMinutes
    },

    /**
     * 日收入改变
     */
    onDailyIncomeChange(e) {
      const value = parseFloat(e.detail.value) || 0
      this.setData({
        dailyIncome: value
      })
    },

    /**
     * 打开日期状态选择器
     */
    onOpenDateTypeSelector() {
      console.log('组件：打开日期状态选择器', {
        statusOptions: this.data.statusOptions,
        selectedIndex: this.data.statusIndex
      })

      this.setData({
        showDateTypeSelector: true
      })
    },

    /**
     * 日期状态选择确认
     */
    onDateTypeSelectorConfirm(e) {
      const { value } = e.detail
      console.log('组件：日期状态选择确认', { value })

      const statusIndex = this.data.statusOptions.findIndex(option => option.value === value)
      const selectedStatusConfig = this.data.statusOptions[statusIndex]

      if (selectedStatusConfig) {
        this.setData({
          statusIndex,
          dateStatus: selectedStatusConfig.value,
          selectedStatusConfig,
          showDateTypeSelector: false
        })
        console.log('组件：更新日期状态完成', {
          dateStatus: selectedStatusConfig.value,
          selectedStatusConfig
        })
      } else {
        console.warn('组件：无效的状态值', value)
      }
    },

    /**
     * 日期状态选择取消
     */
    onDateTypeSelectorCancel() {
      this.setData({
        showDateTypeSelector: false
      })
    },

    /**
     * 日期状态选择关闭
     */
    onDateTypeSelectorClose() {
      this.setData({
        showDateTypeSelector: false
      })
    },

    /**
     * 添加时间段
     */
    onAddTimeInput() {
      const timeInputs = this.data.timeInputs.slice()
      const lastInput = timeInputs[timeInputs.length - 1]

      // 计算新时间段的开始和结束时间，正确处理跨日情况
      let startTime, endTime, isStartNextDay, isEndNextDay

      if (lastInput) {
        // 使用上一个时间段的结束时间作为开始时间
        startTime = lastInput.endTime
        isStartNextDay = lastInput.isEndNextDay

        // 计算结束时间（加1小时）
        const timeResult = this._addHoursWithNextDay(startTime, 1, isStartNextDay)
        endTime = timeResult.time
        isEndNextDay = timeResult.isNextDay
      } else {
        // 默认时间段
        startTime = '09:00'
        endTime = '12:00'
        isStartNextDay = false
        isEndNextDay = false
      }

      let newInput = {
        startTime,
        endTime,
        type: 'work',
        typeIndex: 0,
        income: 0,
        incomeText: '',
        hourlyRate: 0,
        hourlyRateText: '',
        isStartNextDay,
        isEndNextDay,
        _isEditingHourlyRate: false
      }

      if (lastInput) {
        // 如果最后一个时间段是工作或加班，则添加一个休息时间段
        if (lastInput.type === 'work' || lastInput.type === 'overtime') {
          newInput.type = 'rest'
          newInput.typeIndex = 1
        } else if (lastInput.type === 'rest') {
          // 判断是否应该添加加班时间段
          // 需要考虑跨日情况
          const endHour = parseInt(lastInput.endTime.split(':')[0])
          const isLateTime = lastInput.isEndNextDay || (!lastInput.isEndNextDay && endHour >= 18)

          if (isLateTime) {
            newInput.type = 'overtime'
            newInput.typeIndex = 2
          }
        }
      }

      // 为工作和加班时间段设置合理的默认时薪
      if (newInput.type === 'work' || newInput.type === 'overtime') {
        const defaultHourlyRate = this._calculateReasonableHourlyRate(timeInputs)
        newInput.hourlyRate = defaultHourlyRate
        newInput.hourlyRateText = defaultHourlyRate.toFixed(2)
        newInput._lastUpdatedBy = 'hourlyRate' // 标记为基于时薪设置
      }

      timeInputs.push(newInput)

      this.setData({
        timeInputs
      }, () => {
        this._sortTimeInputsByStartTime()
        this._updateAllCalculations()
      })
    },

    /**
     * 删除时间段
     */
    onDeleteTimeInput(e) {
      const { index } = e.currentTarget.dataset
      const timeInputs = this.data.timeInputs.slice()
      timeInputs.splice(index, 1)
      this.setData({
        timeInputs
      }, () => {
        this._updateAllCalculations()
      })
    },

    /**
     * 打开时间范围选择器
     */
    onOpenTimeRangePicker(e) {
      const { index } = e.currentTarget.dataset
      const timeInput = this.data.timeInputs[index]

      console.log('组件：打开时间范围选择器', { index, timeInput })

      this.setData({
        editingTimeIndex: index,
        timeRangeStartTime: timeInput.startTime,
        timeRangeEndTime: timeInput.endTime,
        timeRangeIsStartNextDay: timeInput.isStartNextDay,
        timeRangeIsEndNextDay: timeInput.isEndNextDay,
        showTimeRangePicker: true
      })
    },

    /**
     * 时间范围选择确认
     */
    onTimeRangePickerConfirm(e) {
      const { startTime, endTime, isStartNextDay, isEndNextDay } = e.detail
      const { editingTimeIndex } = this.data

      console.log('组件：时间范围选择确认', {
        editingTimeIndex,
        startTime,
        endTime,
        isStartNextDay,
        isEndNextDay
      })

      if (editingTimeIndex >= 0) {
        const timeInputs = [...this.data.timeInputs]
        timeInputs[editingTimeIndex] = {
          ...timeInputs[editingTimeIndex],
          startTime,
          endTime,
          isStartNextDay,
          isEndNextDay
        }

        this.setData({
          timeInputs,
          editingTimeIndex: -1,
          showTimeRangePicker: false
        }, () => {
          this._updateAllCalculations()
        })
      }
    },

    /**
     * 时间范围选择取消
     */
    onTimeRangePickerCancel() {
      this.setData({
        editingTimeIndex: -1,
        showTimeRangePicker: false
      })
    },

    /**
     * 时间范围选择关闭
     */
    onTimeRangePickerClose() {
      this.setData({
        editingTimeIndex: -1,
        showTimeRangePicker: false
      })
    },

    /**
     * 类型切换（点击循环切换）
     */
    onTypeToggle(e) {
      const { index } = e.currentTarget.dataset
      const timeInputs = [...this.data.timeInputs]

      if (timeInputs[index]) {
        // 循环切换到下一个类型：工作 -> 休息 -> 加班 -> 工作
        const currentTypeIndex = timeInputs[index].typeIndex
        const nextTypeIndex = (currentTypeIndex + 1) % this.data.typeOptions.length

        timeInputs[index].typeIndex = nextTypeIndex
        timeInputs[index].type = this.data.typeOptions[nextTypeIndex].value

        console.log('时间段类型切换:', {
          index,
          from: this.data.typeOptions[currentTypeIndex].text,
          to: this.data.typeOptions[nextTypeIndex].text
        })

        this.setData({ timeInputs }, () => {
          this._updateAllCalculations()
        })
      }
    },

    /**
     * 类型选择改变（保留兼容性）
     */
    onTypeChange(e) {
      const { index } = e.currentTarget.dataset
      const typeIndex = parseInt(e.detail.value)
      const timeInputs = [...this.data.timeInputs]

      if (timeInputs[index]) {
        timeInputs[index].typeIndex = typeIndex
        timeInputs[index].type = this.data.typeOptions[typeIndex].value

        this.setData({ timeInputs }, () => {
          this._updateAllCalculations()
        })
      }
    },

    /**
     * 收入输入改变
     */
    onIncomeChange(e) {
      const { index } = e.currentTarget.dataset
      const value = e.detail.value
      const numValue = parseFloat(value) || 0
      const timeInputs = [...this.data.timeInputs]

      if (timeInputs[index]) {
        timeInputs[index].income = numValue
        timeInputs[index].incomeText = value
        timeInputs[index]._lastUpdatedBy = 'income'

        this.setData({ timeInputs }, () => {
          this._updateAllCalculations()
        })
      }
    },

    /**
     * 时薪输入改变
     */
    onHourlyRateChange(e) {
      const { index } = e.currentTarget.dataset
      const value = e.detail.value
      const numValue = parseFloat(value) || 0
      const timeInputs = [...this.data.timeInputs]

      if (timeInputs[index]) {
        const duration = this._calculateInputDuration(timeInputs[index])
        const income = duration > 0 ? numValue * (duration / 60) : 0

        timeInputs[index].hourlyRate = numValue
        timeInputs[index].hourlyRateText = value
        timeInputs[index].income = Math.round(income * 100) / 100
        timeInputs[index].incomeText = timeInputs[index].income.toString()
        timeInputs[index]._lastUpdatedBy = 'hourlyRate'

        this.setData({ timeInputs }, () => {
          this._updateTotalIncome()
        })
      }
    },

    /**
     * 开始编辑时薪
     */
    onStartEditingHourlyRate(e) {
      const { index } = e.currentTarget.dataset
      const timeInputs = [...this.data.timeInputs]

      if (timeInputs[index]) {
        timeInputs[index]._isEditingHourlyRate = true
        this.setData({ timeInputs })
      }
    },

    /**
     * 结束编辑时薪
     */
    onFinishEditingHourlyRate(e) {
      const { index } = e.currentTarget.dataset
      const timeInputs = [...this.data.timeInputs]

      if (timeInputs[index]) {
        timeInputs[index]._isEditingHourlyRate = false
        this.setData({ timeInputs })
      }
    },

    /**
     * 智能填写收入
     */
    onSmartIncomeCalculate() {
      console.log('组件：打开智能填写收入', {
        timeInputs: this.data.timeInputs
      })

      this.setData({
        showSmartIncomeModal: true
      })
    },

    /**
     * 智能填写收入确认
     */
    onSmartIncomeConfirm(e) {
      const { timeInputs } = e.detail
      console.log('组件：智能填写收入确认', {
        timeInputs,
        timeInputsLength: timeInputs?.length
      })

      this.setData({
        timeInputs,
        showSmartIncomeModal: false
      }, () => {
        this._updateAllCalculations()
        console.log('组件：智能填写收入结果更新完成')
      })
    },

    /**
     * 智能填写收入取消
     */
    onSmartIncomeCancel() {
      this.setData({
        showSmartIncomeModal: false
      })
    },

    /**
     * 智能填写收入关闭
     */
    onSmartIncomeClose() {
      this.setData({
        showSmartIncomeModal: false
      })
    },

    /**
     * 导入安排
     */
    onImportFromDateInModal() {
      console.log('组件：打开导入安排')
      this.setData({
        showScheduleImportModal: true
      })
    },

    /**
     * 导入安排确认
     */
    onScheduleImportConfirm(e) {
      const { sourceDate, targetDate, scheduleData } = e.detail
      console.log('组件：导入安排确认', { sourceDate, targetDate, scheduleData })

      if (!scheduleData || !scheduleData.segments) {
        console.warn('组件：导入数据无效')
        wx.showToast({
          title: '导入数据无效',
          icon: 'none'
        })
        return
      }

      // 转换导入的数据为组件格式
      const timeInputs = scheduleData.segments.map(segment => ({
        startTime: this._minutesToTimeDisplay(segment.start).replace('次日', ''),
        endTime: this._minutesToTimeDisplay(segment.end).replace('次日', ''),
        type: segment.type,
        typeIndex: this.data.typeOptions.findIndex(option => option.value === segment.type),
        income: segment.income || 0,
        incomeText: (segment.income || 0).toString(),
        hourlyRate: 0,
        hourlyRateText: '',
        isStartNextDay: segment.start >= 24 * 60,
        isEndNextDay: segment.end >= 24 * 60,
        _isEditingHourlyRate: false
      }))

      // 更新日收入和状态
      const dailyIncome = scheduleData.dailyIncome || 0
      const dateStatus = scheduleData.status || this.data.dateStatus
      const statusIndex = this.data.statusOptions.findIndex(option => option.value === dateStatus)
      const selectedStatusConfig = this.data.statusOptions[statusIndex] || this.data.selectedStatusConfig

      this.setData({
        timeInputs,
        dailyIncome,
        dateStatus,
        statusIndex: statusIndex >= 0 ? statusIndex : this.data.statusIndex,
        selectedStatusConfig,
        showScheduleImportModal: false
      }, () => {
        this._updateAllCalculations()
        console.log('组件：导入安排完成', {
          timeInputsLength: timeInputs.length,
          dailyIncome,
          dateStatus
        })
      })
    },

    /**
     * 导入安排取消
     */
    onScheduleImportCancel() {
      this.setData({
        showScheduleImportModal: false
      })
    },

    /**
     * 导入安排关闭
     */
    onScheduleImportClose() {
      this.setData({
        showScheduleImportModal: false
      })
    },

    /**
     * 确认保存
     */
    onConfirmSchedule() {

      if (this.data.hasTimeConflict) {
        wx.showToast({
          title: '存在时间冲突，无法保存',
          icon: 'none'
        })
        return
      }

      // 验证时间输入
      const validationResult = this._validateTimeInputs(this.data.timeInputs)
      if (!validationResult.isValid) {
        wx.showToast({
          title: validationResult.message,
          icon: 'none'
        })
        return
      }

      // 处理收入数据，确保小数位数不超过2位
      const processedTimeInputs = this._processIncomeData(this.data.timeInputs)

      try {
        // 将字符串转换为 Date 对象
        const selectedDate = new Date(this.properties.selectedDate)

        // 先保存日期状态，避免被工作计划保存覆盖
        this.timeSegmentService.setDateStatus(selectedDate, this.data.dateStatus, this.data.currentWorkId)

        // 保存工作计划，检查摸鱼冲突
        const result = this.timeSegmentService.setDaySchedule(
          selectedDate,
          processedTimeInputs,
          this.data.dailyIncome,
          this.data.currentWorkId
        )

        // 检查是否有摸鱼数据冲突
        if (result && result.requiresFishingUpdate) {
          console.log('设置工作计划组件：检测到摸鱼冲突，共', result.conflicts.length, '条')
          this._handleFishingConflicts(result)
          return
        }

        wx.showToast({
          title: '保存成功',
          icon: 'success'
        })

        // 通知页面数据已更新，需要刷新
        this.triggerEvent('dataUpdated', {
          selectedDate: this.properties.selectedDate
        })

        this.onClose()
      } catch (error) {
        console.error('设置工作计划组件：保存失败', error)
        wx.showToast({
          title: '保存失败',
          icon: 'none'
        })
      }
    },

    /**
     * 取消
     */
    onCancel() {
      this.triggerEvent('cancel')
      this.onClose()
    },

    /**
     * 关闭模态框
     */
    onClose() {
      // 开始出场动画
      this.setData({ modalVisible: false })

      // 等待动画完成后触发关闭事件
      setTimeout(() => {
        this.triggerEvent('close')
      }, 300) // 与CSS动画时长一致
    },

    /**
     * 阻止事件冒泡
     */
    onStopPropagation() {
      // 阻止点击模态框内容时关闭模态框
    },

    /**
     * 验证时间输入的合法性
     */
    _validateTimeInputs(timeInputs) {
      for (let i = 0; i < timeInputs.length; i++) {
        const input = timeInputs[i]

        // 检查时间是否填写完整
        if (!input.startTime || !input.endTime) {
          return {
            isValid: false,
            message: `第${i + 1}个时间段的时间未填写完整`
          }
        }

        // 计算时间段持续时间
        const duration = this._calculateInputDuration(input)

        // 检查时间段持续时间是否大于0
        if (duration <= 0) {
          return {
            isValid: false,
            message: `第${i + 1}个时间段的结束时间必须晚于开始时间`
          }
        }

        // 检查时间段持续时间是否过短（小于1分钟）
        if (duration < 1) {
          return {
            isValid: false,
            message: `第${i + 1}个时间段的持续时间不能少于1分钟`
          }
        }

        // 检查时间段持续时间是否过长（超过48小时）
        if (duration > 48 * 60) {
          return {
            isValid: false,
            message: `第${i + 1}个时间段的持续时间不能超过48小时`
          }
        }

        // 检查收入数据的合法性
        if (input.type !== 'rest') {
          if (input.income < 0) {
            return {
              isValid: false,
              message: `第${i + 1}个时间段的收入不能为负数`
            }
          }

          // 检查收入是否超过合理范围（例如单个时间段不超过10万元）
          if (input.income > 1000000000) {
            return {
              isValid: false,
              message: `第${i + 1}个时间段的收入不能超过1000,000,000元`
            }
          }
        }
      }

      return {
        isValid: true,
        message: ''
      }
    },

    /**
     * 处理收入数据，确保小数位数不超过2位
     */
    _processIncomeData(timeInputs) {
      return timeInputs.map(input => {
        const processedInput = { ...input }

        if (input.type !== 'rest' && typeof input.income === 'number') {
          // 限制收入最多保留2位小数
          processedInput.income = Math.round(input.income * 100) / 100

          // 重新计算时薪
          const duration = this._calculateInputDuration(input)
          if (duration > 0) {
            const hourlyRate = processedInput.income / (duration / 60)
            processedInput.hourlyRate = Math.round(hourlyRate * 100) / 100
            processedInput.hourlyRateText = processedInput.hourlyRate.toFixed(2)
            processedInput._lastUpdatedBy = 'income' // 标记为基于收入计算
          }
        }

        return processedInput
      })
    },

    /**
     * 初始化服务
     */
    _initializeServices() {
      // 直接获取服务
      const app = getApp()
      this.timeSegmentService = app.getTimeSegmentService()
      this.dataManager = app.getDataManager()

      this._loadCurrentWorkId()
    },

    /**
     * 处理摸鱼数据冲突
     */
    _handleFishingConflicts(result) {
      const conflictCount = result.conflicts.length
      const conflictMessages = result.conflicts.map(conflict => {
        let message = `• ${conflict.fishingTime}`
        if (conflict.fishingRemark) {
          message += ` ${conflict.fishingRemark}`
        }
        return message
      }).join('\n')

      const content = `新的时间安排未能包含现有摸鱼记录：\n\n${conflictMessages}\n\n点击"删除"将删除这${conflictCount}条摸鱼记录并保存时间安排。`

      wx.showModal({
        title: '摸鱼记录冲突',
        content: content,
        confirmText: '删除',
        cancelText: '取消',
        confirmColor: '#ff6b6b',
        success: (res) => {
          if (res.confirm) {
            // 删除冲突记录并保存
            this._deleteConflictingFishingRecords(result.conflicts)
          }
          // 如果取消，不做任何操作，保持模态框打开状态
        }
      })
    },

    /**
     * 删除冲突的摸鱼记录
     */
    _deleteConflictingFishingRecords(conflicts) {
      try {
        const dataManager = getApp().getDataManager()
        const selectedDate = new Date(this.properties.selectedDate)

        // 删除所有冲突的摸鱼记录
        conflicts.forEach(conflict => {
          dataManager.deleteFishing(this.data.currentWorkId, selectedDate, conflict.fishingId)
        })

        // 先保存日期状态
        this.timeSegmentService.setDateStatus(selectedDate, this.data.dateStatus, this.data.currentWorkId)

        // 重新尝试保存时间段
        const processedTimeInputs = this._processIncomeData(this.data.timeInputs)
        this.timeSegmentService.setDaySchedule(
          selectedDate,
          processedTimeInputs,
          this.data.dailyIncome,
          this.data.currentWorkId
        )

        wx.showToast({
          title: `已删除${conflicts.length}条冲突记录并保存`,
          icon: 'success',
          duration: 3000
        })

        // 通知页面数据已更新，需要刷新
        this.triggerEvent('dataUpdated', {
          selectedDate: this.properties.selectedDate
        })

        this.onClose()
      } catch (error) {
        console.error('删除冲突摸鱼记录失败:', error)
        wx.showToast({
          title: '处理冲突失败',
          icon: 'error'
        })
      }
    },

    /**
     * 创建带有合理收入分配的默认时间段
     */
    _createDefaultTimeInputsWithIncome(targetDailyIncome) {
      // 定义默认时间段结构
      const defaultSegments = [
        {
          startTime: '09:00',
          endTime: '12:00',
          type: 'work',
          typeIndex: 0
        },
        {
          startTime: '12:00',
          endTime: '14:00',
          type: 'rest',
          typeIndex: 1
        },
        {
          startTime: '14:00',
          endTime: '18:00',
          type: 'work',
          typeIndex: 0
        }
      ]

      // 计算总工作时长（分钟）
      let totalWorkMinutes = 0
      defaultSegments.forEach(segment => {
        if (segment.type === 'work') {
          const [startHour, startMinute] = segment.startTime.split(':').map(Number)
          const [endHour, endMinute] = segment.endTime.split(':').map(Number)
          const duration = (endHour * 60 + endMinute) - (startHour * 60 + startMinute)
          totalWorkMinutes += duration
        }
      })

      // 计算平均时薪
      const averageHourlyRate = totalWorkMinutes > 0 ? (targetDailyIncome / (totalWorkMinutes / 60)) : 60

      // 创建时间段对象并分配收入
      return defaultSegments.map(segment => {
        const timeInput = {
          startTime: segment.startTime,
          endTime: segment.endTime,
          type: segment.type,
          typeIndex: segment.typeIndex,
          isStartNextDay: false,
          isEndNextDay: false,
          _lastUpdatedBy: 'income' // 标记为基于收入计算的
        }

        if (segment.type === 'work') {
          // 计算这个工作时间段的持续时间
          const [startHour, startMinute] = segment.startTime.split(':').map(Number)
          const [endHour, endMinute] = segment.endTime.split(':').map(Number)
          const duration = (endHour * 60 + endMinute) - (startHour * 60 + startMinute)

          // 按比例分配收入
          const segmentIncome = Math.round((duration / totalWorkMinutes) * targetDailyIncome * 100) / 100

          timeInput.income = segmentIncome
          timeInput.incomeText = segmentIncome.toString()
          timeInput.hourlyRate = Math.round(averageHourlyRate * 100) / 100
          timeInput.hourlyRateText = timeInput.hourlyRate.toFixed(2)
        } else {
          // 休息时间段
          timeInput.income = 0
          timeInput.incomeText = ''
          timeInput.hourlyRate = 0
          timeInput.hourlyRateText = '0.00'
        }

        return timeInput
      })
    },

    /**
     * 计算合理的默认时薪
     */
    _calculateReasonableHourlyRate(existingTimeInputs) {
      // 1. 优先使用现有时间段的平均时薪
      const workSegments = existingTimeInputs.filter(input =>
        (input.type === 'work' || input.type === 'overtime') && input.hourlyRate > 0
      )

      if (workSegments.length > 0) {
        const totalHourlyRate = workSegments.reduce((sum, input) => sum + input.hourlyRate, 0)
        return Math.round((totalHourlyRate / workSegments.length) * 100) / 100
      }

      // 2. 如果没有现有时薪，使用日收入目标计算
      const dailyIncome = this.data.dailyIncome || 500
      const estimatedWorkHours = 8 // 假设一天工作8小时
      const estimatedHourlyRate = dailyIncome / estimatedWorkHours

      return Math.round(estimatedHourlyRate * 100) / 100
    },

    /**
     * 添加一小时到时间字符串
     */
    _addHours(timeStr, hours) {
      const [hour, minute] = timeStr.split(':').map(Number)
      const newHour = hour + hours
      return `${String(newHour).padStart(2, '0')}:${String(minute).padStart(2, '0')}`
    },

    /**
     * 添加小时数到时间字符串，正确处理跨日情况
     */
    _addHoursWithNextDay(timeStr, hours, isCurrentNextDay = false) {
      const [hour, minute] = timeStr.split(':').map(Number)
      let totalHours = hour + hours
      let isNextDay = isCurrentNextDay

      // 处理时间超过24小时的情况
      if (totalHours >= 24) {
        totalHours = totalHours % 24
        isNextDay = true
      }

      // 处理时间小于0的情况（虽然在添加时间时不太可能发生）
      if (totalHours < 0) {
        totalHours = 24 + totalHours
        isNextDay = false
      }

      return {
        time: `${String(totalHours).padStart(2, '0')}:${String(minute).padStart(2, '0')}`,
        isNextDay
      }
    },

    /**
     * 按开始时间排序时间段
     */
    _sortTimeInputsByStartTime() {
      const timeInputs = this.data.timeInputs.slice()

      timeInputs.sort((a, b) => {
        const aMinutes = this._timeToMinutes(a.startTime, a.isStartNextDay)
        const bMinutes = this._timeToMinutes(b.startTime, b.isStartNextDay)
        return aMinutes - bMinutes
      })

      this.setData({ timeInputs })
    },

    /**
     * 加载当前工作ID
     */
    _loadCurrentWorkId() {
      try {
        if (this.dataManager) {
          const currentWork = this.dataManager.getCurrentWork()
          const currentWorkId = currentWork ? currentWork.id : ''
          this.setData({ currentWorkId })
          console.log('设置工作计划组件：当前工作ID', currentWorkId)
        }
      } catch (error) {
        console.warn('设置工作计划组件：获取当前工作ID失败', error)
      }
    }
  },

  /**
   * 组件生命周期
   */
  lifetimes: {
    attached() {
      console.log('设置工作计划组件已加载')
      this._initializeServices()
    }
  },

  /**
   * 组件观察器
   */
  observers: {
    'visible': function(visible) {
      this._onPropertiesChange()
    }
  }
})
