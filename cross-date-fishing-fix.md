# 跨日期摸鱼功能修复

## 问题描述

用户反馈摸鱼功能存在跨日期时间段处理问题：

1. **错误的时间段检测**：当今天还处于昨天的跨日时间段时（如昨天22:00-今天02:00），点击开始摸鱼检测的是今天的时间段，而不是昨天的跨日时间段
2. **错误的数据归属**：摸鱼记录被错误地保存到今天的日期中，而应该保存到昨天
3. **图表显示问题**：结束摸鱼之前，时间可视化图表不会显示当前摸鱼时间段

## 根本原因分析

### 原有逻辑的问题
```javascript
// 原有的startFishing方法
startFishing(remark = '') {
  const today = new Date()  // 总是使用今天的日期
  const dayData = this.getDayData(currentWork.id, today)  // 获取今天的时间段
  return this.fishingManager.startFishing(currentWork.id, today, dayData.segments, remark)
}
```

### 问题场景
```
时间轴：昨天22:00 -------- 今天00:00 -------- 今天02:00
        |              |              |
        加班开始        日期分界线      加班结束

当前时间：今天01:00
- 用户实际在昨天的加班时间段中
- 但系统检测今天的时间段（没有找到）
- 或者找到了今天的其他时间段（错误）
```

## 解决方案

### 1. 添加跨日期工作状态检测方法

在`data-manager.js`中添加`detectCrossDateWorkStatus()`方法：

```javascript
detectCrossDateWorkStatus() {
  // 1. 首先检查今天的时间段
  // 2. 如果今天没有匹配的时间段，检查昨天的跨日期时间段
  // 3. 返回正确的工作日期、时间段数组和当前工作时间段
}
```

### 2. 修改开始摸鱼逻辑

修改`startFishing`方法使用新的检测逻辑：

```javascript
startFishing(remark = '') {
  // 使用跨日期检测
  const workStatus = this.detectCrossDateWorkStatus()
  
  if (!workStatus.workSegment) {
    return { success: false, message: '当前不在工作时间内，无法开始摸鱼' }
  }

  // 使用正确的工作日期和时间段
  return this.fishingManager.startFishing(
    currentWork.id, 
    workStatus.workDate,     // 可能是昨天的日期
    workStatus.segments,     // 对应日期的时间段
    remark
  )
}
```

### 3. 增强时间段查找逻辑

在`fishing-manager.js`中添加`findCurrentWorkSegment()`方法：

```javascript
findCurrentWorkSegment(segments, currentMinutes) {
  for (const segment of segments) {
    if (segment.type === 'rest') continue

    const isCrossDate = segment.start >= 1440 || segment.end >= 1440
    
    if (isCrossDate) {
      // 处理跨日期时间段
      if (segment.start >= 1440) {
        // 整个时间段都在次日
        const startTimeToday = segment.start - 1440
        const endTimeToday = segment.end >= 1440 ? segment.end - 1440 : segment.end
        if (currentMinutes >= startTimeToday && currentMinutes <= endTimeToday) {
          return segment
        }
      } else {
        // 时间段跨越两天
        const endTimeToday = segment.end >= 1440 ? segment.end - 1440 : segment.end
        if (currentMinutes <= endTimeToday) {
          return segment
        }
      }
    } else {
      // 普通时间段
      if (currentMinutes >= segment.start && currentMinutes <= segment.end) {
        return segment
      }
    }
  }
  return null
}
```

### 4. 确保图表正确更新

修改dashboard1的摸鱼开始事件处理：

```javascript
onFishingStart: function(e) {
  this.updateCurrentFishingState()
  this.loadTodaySchedule()  // 重新加载，确保显示正确的数据
  this.updateCurrentTime()
}
```

## 修改的文件

### 1. miniprogram/core/managers/data-manager.js
- 添加`detectCrossDateWorkStatus()`方法
- 修改`startFishing()`方法使用新的检测逻辑

### 2. miniprogram/core/managers/fishing-manager.js
- 添加`findCurrentWorkSegment()`方法
- 修改`startFishing()`方法使用新的时间段查找逻辑

### 3. miniprogram/components/dashboard1/index.js
- 修改`onFishingStart()`方法，确保重新加载时间安排

## 修复效果

### 修复前的问题
```
场景：昨天22:00-今天02:00加班，当前时间今天01:00

1. 点击开始摸鱼
2. 系统检测今天的时间段 → 没有找到或找错
3. 摸鱼记录保存到今天 → 错误的日期归属
4. 图表显示今天的数据 → 看不到当前摸鱼
```

### 修复后的正确行为
```
场景：昨天22:00-今天02:00加班，当前时间今天01:00

1. 点击开始摸鱼
2. 系统检测到在昨天的跨日期时间段中 → 正确识别
3. 摸鱼记录保存到昨天 → 正确的日期归属
4. 图表显示昨天的数据，包含当前摸鱼 → 正确显示
```

## 测试场景

### 测试场景1：跨日期加班摸鱼
```
前置条件：
- 昨天设置加班时间段：22:00-次日02:00
- 当前时间：今天01:00

测试步骤：
1. 点击开始摸鱼
2. 检查摸鱼是否成功开始
3. 检查时间图表是否显示当前摸鱼时间段
4. 结束摸鱼，检查记录是否保存到昨天

预期结果：
- 摸鱼成功开始
- 图表显示昨天的数据，包含当前摸鱼
- 摸鱼记录保存到昨天的日期下
```

### 测试场景2：跨日期工作时间摸鱼
```
前置条件：
- 昨天设置工作时间段：23:00-次日01:00
- 当前时间：今天00:30

测试步骤：
1. 点击开始摸鱼
2. 验证摸鱼状态和图表显示

预期结果：
- 摸鱼记录归属到昨天
- 图表正确显示跨日期时间段和摸鱼状态
```

### 测试场景3：普通时间段摸鱼（回归测试）
```
前置条件：
- 今天设置工作时间段：09:00-18:00
- 当前时间：今天14:00

测试步骤：
1. 点击开始摸鱼
2. 验证功能正常

预期结果：
- 功能与之前完全一致
- 摸鱼记录保存到今天
```

## 兼容性说明

### 向后兼容
- 不影响现有的摸鱼记录数据
- 普通时间段的摸鱼功能完全不变
- 只增强了跨日期时间段的处理

### 数据一致性
- 摸鱼记录的数据结构保持不变
- 时间计算逻辑保持一致
- 图表显示逻辑保持兼容

## 总结

通过这次修复，解决了跨日期时间段中摸鱼功能的三个核心问题：

1. **正确的时间段检测**：能够识别当前时间是否在昨天的跨日期时间段中
2. **正确的数据归属**：摸鱼记录保存到正确的日期下
3. **正确的图表显示**：时间可视化图表能够实时显示当前摸鱼状态

修复后的功能既保持了原有功能的完整性，又正确处理了跨日期的复杂场景，提升了用户体验的一致性和数据的准确性。
